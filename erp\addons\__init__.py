"""
ERP Addons Package
Provides standardized addon import system ensuring erp.addons.addon_name is available
regardless of addon paths configuration.
"""
import sys
import os
import importlib
import importlib.util
from typing import Dict, Any, Optional
from .loader import <PERSON>don<PERSON>oader
from .manifest import AddonManifest

__all__ = ['AddonLoader', 'AddonManifest', 'get_addon_module', 'ensure_addon_import']


class AddonImportManager:
    """Manages standardized addon imports for erp.addons.addon_name pattern"""

    _instance: Optional['AddonImportManager'] = None
    _addon_modules: Dict[str, Any] = {}
    _addon_paths: Dict[str, str] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
            self._setup_addon_namespace()

    def _setup_addon_namespace(self):
        """Setup the erp.addons namespace for standardized imports"""
        # Ensure erp.addons is properly set up as a namespace package
        if 'erp.addons' not in sys.modules:
            # Create the namespace module
            namespace_module = importlib.util.module_from_spec(
                importlib.util.spec_from_loader('erp.addons', loader=None)
            )
            sys.modules['erp.addons'] = namespace_module

    def register_addon_path(self, addon_name: str, addon_path: str):
        """Register an addon path for standardized import"""
        self._addon_paths[addon_name] = addon_path

        # Create the standardized module path
        module_name = f'erp.addons.{addon_name}'

        if module_name not in sys.modules:
            try:
                # Try to import from the addon path
                init_file = os.path.join(addon_path, '__init__.py')
                if os.path.exists(init_file):
                    spec = importlib.util.spec_from_file_location(module_name, init_file)
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        sys.modules[module_name] = module
                        spec.loader.exec_module(module)
                        self._addon_modules[addon_name] = module

                        # Also register the addon under its direct name for backward compatibility
                        if addon_name not in sys.modules:
                            sys.modules[addon_name] = module

                        return module
            except Exception as e:
                print(f"Warning: Failed to register addon {addon_name}: {e}")

        return sys.modules.get(module_name)

    def get_addon_module(self, addon_name: str) -> Optional[Any]:
        """Get an addon module by name"""
        # Try standardized import first
        module_name = f'erp.addons.{addon_name}'
        if module_name in sys.modules:
            return sys.modules[module_name]

        # Try direct addon name
        if addon_name in sys.modules:
            return sys.modules[addon_name]

        # Try to load from registered path
        if addon_name in self._addon_paths:
            return self.register_addon_path(addon_name, self._addon_paths[addon_name])

        return None

    def ensure_addon_import(self, addon_name: str, addon_path: str = None) -> bool:
        """Ensure an addon can be imported via erp.addons.addon_name"""
        if addon_path:
            self.register_addon_path(addon_name, addon_path)

        module = self.get_addon_module(addon_name)
        return module is not None

    def list_registered_addons(self) -> Dict[str, str]:
        """List all registered addon paths"""
        return self._addon_paths.copy()


# Global instance
_addon_import_manager = AddonImportManager()


def get_addon_module(addon_name: str) -> Optional[Any]:
    """Get an addon module by name using standardized import system"""
    return _addon_import_manager.get_addon_module(addon_name)


def ensure_addon_import(addon_name: str, addon_path: str = None) -> bool:
    """Ensure an addon can be imported via erp.addons.addon_name"""
    return _addon_import_manager.ensure_addon_import(addon_name, addon_path)
