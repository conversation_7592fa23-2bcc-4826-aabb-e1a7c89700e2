"""
Common middleware utilities
"""
from fastapi import Request, HTTPException
from typing import Callable, Any
import time
import logging
from ..database.async_registry import AsyncDatabaseRegistry
from .responses import handle_database_error, handle_generic_error

logger = logging.getLogger(__name__)


async def database_middleware(request: Request, call_next: Callable) -> Any:
    """Database connection middleware"""
    from ..config import config

    # Determine database name based on configuration mode
    db_name = None

    if config.is_single_db_mode:
        # Single database mode: use configured database
        db_name = config.get_default_database()
    else:
        # Multi-database mode: extract from request
        db_name = request.headers.get('X-Database')
        if not db_name:
            # Try to get from query params
            db_name = request.query_params.get('db')

        # Validate database name against filter in multi-db mode
        if db_name:
            import re
            db_filter = config.db_filter
            if not re.match(db_filter, db_name):
                logger.warning(f"Database {db_name} does not match filter {db_filter}")
                db_name = None

    # If no database name determined, this is an error
    if not db_name:
        if config.is_single_db_mode:
            logger.error("No database configured in single database mode")
            raise ValueError("Database not configured")
        else:
            logger.error("No database specified in multi-database mode")
            raise ValueError("Database not specified")

    # Set current database
    AsyncDatabaseRegistry.set_current_database(db_name)
    request.state.db_name = db_name

    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"Database middleware error: {e}")
        raise handle_database_error(e)


async def timing_middleware(request: Request, call_next: Callable) -> Any:
    """Request timing middleware"""
    start_time = time.perf_counter()
    
    response = await call_next(request)
    
    process_time = time.perf_counter() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


async def error_handling_middleware(request: Request, call_next: Callable) -> Any:
    """Global error handling middleware"""
    try:
        response = await call_next(request)
        return response
    except HTTPException:
        # Re-raise HTTP exceptions (they're handled by FastAPI)
        raise
    except Exception as e:
        logger.error(f"Unhandled error in {request.url}: {e}", exc_info=True)
        raise handle_generic_error(e)


async def logging_middleware(request: Request, call_next: Callable) -> Any:
    """Request logging middleware"""
    start_time = time.perf_counter()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        
        # Log response
        process_time = time.perf_counter() - start_time
        logger.info(
            f"Response: {response.status_code} "
            f"({process_time:.3f}s) {request.method} {request.url}"
        )
        
        return response
    except Exception as e:
        process_time = time.perf_counter() - start_time
        logger.error(
            f"Error: {type(e).__name__} "
            f"({process_time:.3f}s) {request.method} {request.url}: {e}"
        )
        raise
