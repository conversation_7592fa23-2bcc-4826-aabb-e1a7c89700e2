"""
WSGI Server for ERP system
"""
from flask import Flask, request, jsonify, g
import json
from typing import Dict, Any, Optional
from .config import config
from .database.registry import DatabaseRegistry
from .addons.loader import <PERSON>donLoader
from .models.base import ModelRegistry


class ERPServer:
    """Main ERP server application"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.addon_loader = AddonLoader()
        self._setup_routes()
        self._load_addons()
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.before_request
        def before_request():
            """Setup database connection before each request"""
            # Determine database name based on configuration mode
            db_name = None

            if config.is_single_db_mode:
                # Single database mode: use configured database
                db_name = config.get_default_database()
            else:
                # Multi-database mode: extract from request
                db_name = request.headers.get('X-Database')
                if not db_name:
                    # Try to get from query params
                    db_name = request.args.get('db')

                # Validate database name against filter in multi-db mode
                if db_name:
                    import re
                    db_filter = config.db_filter
                    if not re.match(db_filter, db_name):
                        db_name = None

            # If no database name determined, this is an error
            if not db_name:
                if config.is_single_db_mode:
                    raise ValueError("Database not configured")
                else:
                    raise ValueError("Database not specified")

            DatabaseRegistry.set_current_database(db_name)
            g.db = DatabaseRegistry.get_current_database()
        
        @self.app.route('/')
        def index():
            """Root endpoint"""
            return jsonify({
                'message': 'ERP System',
                'version': '1.0.0',
                'status': 'running',
                'server_type': 'wsgi',
                'database_mode': 'single' if config.is_single_db_mode else 'multi',
                'default_database': config.get_default_database() if config.is_single_db_mode else None
            })
        
        @self.app.route('/web/database/list', methods=['GET'])
        def list_databases():
            """List available databases"""
            if not config.list_db:
                return jsonify({'error': 'Database listing disabled'}), 403
            
            databases = DatabaseRegistry.list_databases()
            return jsonify({'databases': databases})
        
        @self.app.route('/web/dataset/call_kw', methods=['POST'])
        def call_kw():
            """Handle model method calls"""
            try:
                data = request.get_json()
                model_name = data.get('model')
                method = data.get('method')
                args = data.get('args', [])
                kwargs = data.get('kwargs', {})
                
                # Get model class
                model_class = ModelRegistry.get(model_name)
                if not model_class:
                    return jsonify({'error': f'Model {model_name} not found'}), 404
                
                # Call method
                if hasattr(model_class, method):
                    result = getattr(model_class, method)(*args, **kwargs)
                    return jsonify({'result': result})
                else:
                    return jsonify({'error': f'Method {method} not found'}), 404
                    
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/web/dataset/search_read', methods=['POST'])
        def search_read():
            """Search and read records"""
            try:
                data = request.get_json()
                model_name = data.get('model')
                domain = data.get('domain', [])
                fields = data.get('fields', [])
                limit = data.get('limit', 80)
                offset = data.get('offset', 0)
                
                # Get model class
                model_class = ModelRegistry.get(model_name)
                if not model_class:
                    return jsonify({'error': f'Model {model_name} not found'}), 404
                
                # Search records (placeholder implementation)
                records = model_class.search(domain)
                
                # Read fields
                result = []
                for record in records[offset:offset+limit]:
                    result.append(record.read(fields))
                
                return jsonify({
                    'records': result,
                    'length': len(records)
                })
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/web/action/load', methods=['POST'])
        def load_action():
            """Load action definition"""
            try:
                data = request.get_json()
                action_id = data.get('action_id')
                
                # Placeholder action loading
                return jsonify({
                    'id': action_id,
                    'type': 'ir.actions.act_window',
                    'name': 'Action',
                    'res_model': 'ir.model',
                    'view_mode': 'tree,form',
                })
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/web/session/authenticate', methods=['POST'])
        def authenticate():
            """Authenticate user session"""
            try:
                data = request.get_json()
                db = data.get('db')
                login = data.get('login')
                password = data.get('password')
                
                # Simple authentication (placeholder)
                if login == 'admin' and password == config.get('options', 'admin_passwd', 'admin'):
                    return jsonify({
                        'uid': 1,
                        'username': login,
                        'session_id': 'session_123',
                        'db': db,
                    })
                else:
                    return jsonify({'error': 'Invalid credentials'}), 401
                    
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/web/session/get_session_info', methods=['POST'])
        def get_session_info():
            """Get session information"""
            return jsonify({
                'uid': 1,
                'username': 'admin',
                'db': config.get('options', 'db_name'),
                'server_version': '1.0.0',
            })
        
        @self.app.route('/addons', methods=['GET'])
        def list_addons():
            """List loaded addons"""
            addons = self.addon_loader.get_loaded_addons()
            result = {}
            for name, manifest in addons.items():
                result[name] = manifest.to_dict()
            return jsonify(result)
        
        @self.app.route('/models', methods=['GET'])
        def list_models():
            """List registered models"""
            models = ModelRegistry.all()
            result = {}
            for name, model_class in models.items():
                result[name] = {
                    'name': name,
                    'description': getattr(model_class, '_description', ''),
                    'table': getattr(model_class, '_table', ''),
                    'fields': model_class.fields_get(),
                }
            return jsonify(result)
    
    def _load_addons(self):
        """Load all addons"""
        print("Loading addons...")
        success = self.addon_loader.load_addons()
        if success:
            print(f"Loaded {len(self.addon_loader.loaded_addons)} addons")
        else:
            print("Failed to load some addons")
    
    def run(self, host=None, port=None, debug=False):
        """Run the server"""
        server_config = config.server_config
        host = host or server_config['host']
        port = port or server_config['port']
        
        print(f"Starting ERP server on {host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


def create_app():
    """Create Flask application"""
    server = ERPServer()
    return server.app


if __name__ == '__main__':
    server = ERPServer()
    server.run(debug=True)
