"""
Test isolation system for ERP testing framework
Provides proper isolation between test runs and addon tests
"""
import sys
import os
import tempfile
import shutil
from typing import Dict, Any, Optional, Set, List
from contextlib import contextmanager
from unittest.mock import patch

from erp.models.base import ModelRegistry
from erp.config import config

try:
    from erp.models.async_base import AsyncModelRegistry
    from erp.database.async_registry import AsyncDatabaseRegistry
    ASYNC_AVAILABLE = True
except ImportError:
    ASYNC_AVAILABLE = False
    AsyncModelRegistry = None
    AsyncDatabaseRegistry = None


class TestIsolation:
    """Manages test isolation for ERP tests"""
    
    def __init__(self):
        self._original_state = {}
        self._temp_dirs = []
        self._patches = []
    
    def setup_isolation(self):
        """Setup test isolation"""
        self._save_original_state()
        self._setup_test_environment()
    
    def teardown_isolation(self):
        """Teardown test isolation"""
        self._restore_original_state()
        self._cleanup_temp_dirs()
        self._cleanup_patches()
    
    def _save_original_state(self):
        """Save original system state"""
        # Save model registries
        self._original_state['models'] = ModelRegistry._models.copy()
        if ASYNC_AVAILABLE:
            self._original_state['async_models'] = AsyncModelRegistry._models.copy()
        
        # Save configuration
        self._original_state['config'] = {}
        for section in config.sections():
            self._original_state['config'][section] = {}
            for option in config.options(section):
                self._original_state['config'][section][option] = config.get(section, option)
        
        # Save sys.path
        self._original_state['sys_path'] = sys.path.copy()
        
        # Save sys.modules (addon modules only)
        self._original_state['addon_modules'] = {}
        for module_name in list(sys.modules.keys()):
            if (module_name.startswith('erp.addons.') or 
                any(module_name.startswith(addon) for addon in self._get_addon_names())):
                self._original_state['addon_modules'][module_name] = sys.modules[module_name]
    
    def _restore_original_state(self):
        """Restore original system state"""
        # Restore model registries
        ModelRegistry._models = self._original_state.get('models', {})
        if ASYNC_AVAILABLE:
            AsyncModelRegistry._models = self._original_state.get('async_models', {})
        
        # Restore configuration
        original_config = self._original_state.get('config', {})
        for section_name, section_data in original_config.items():
            if not config.has_section(section_name):
                config.add_section(section_name)
            for option, value in section_data.items():
                config.set(section_name, option, value)
        
        # Restore sys.path
        sys.path[:] = self._original_state.get('sys_path', sys.path)
        
        # Clean up addon modules from sys.modules
        addon_modules = self._original_state.get('addon_modules', {})
        for module_name in list(sys.modules.keys()):
            if (module_name.startswith('erp.addons.') or 
                any(module_name.startswith(addon) for addon in self._get_addon_names())):
                if module_name not in addon_modules:
                    del sys.modules[module_name]
    
    def _setup_test_environment(self):
        """Setup test environment"""
        # Clear model registries
        ModelRegistry._models.clear()
        if ASYNC_AVAILABLE:
            AsyncModelRegistry._models.clear()
        
        # Setup test configuration
        self._setup_test_config()
    
    def _setup_test_config(self):
        """Setup test-specific configuration"""
        # Set test database
        config.set('options', 'db_name', 'erp_test')
        
        # Set test addons path
        config.set('options', 'addons_path', 'addons')
        
        # Disable certain features for testing
        config.set('options', 'log_level', 'WARNING')
    
    def _get_addon_names(self) -> List[str]:
        """Get list of addon names"""
        addons_path = config.get('options', 'addons_path', fallback='addons')
        if not os.path.exists(addons_path):
            return []
        
        return [
            name for name in os.listdir(addons_path)
            if os.path.isdir(os.path.join(addons_path, name))
            and not name.startswith('.')
        ]
    
    def create_temp_dir(self, prefix: str = 'erp_test_') -> str:
        """Create a temporary directory for testing"""
        temp_dir = tempfile.mkdtemp(prefix=prefix)
        self._temp_dirs.append(temp_dir)
        return temp_dir
    
    def _cleanup_temp_dirs(self):
        """Cleanup temporary directories"""
        for temp_dir in self._temp_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
        self._temp_dirs.clear()
    
    def _cleanup_patches(self):
        """Cleanup mock patches"""
        for patch_obj in self._patches:
            patch_obj.stop()
        self._patches.clear()
    
    def patch_config(self, section: str, option: str, value: str):
        """Patch configuration for testing"""
        patch_obj = patch.object(config, 'get')
        mock_get = patch_obj.start()
        
        def side_effect(sect, opt, **kwargs):
            if sect == section and opt == option:
                return value
            return self._original_state['config'].get(sect, {}).get(opt, kwargs.get('fallback'))
        
        mock_get.side_effect = side_effect
        self._patches.append(patch_obj)
    
    @contextmanager
    def isolated_addon_test(self, addon_name: str):
        """Context manager for isolated addon testing"""
        # Save current addon state
        addon_modules = {}
        for module_name in list(sys.modules.keys()):
            if module_name.startswith(f'erp.addons.{addon_name}') or module_name == addon_name:
                addon_modules[module_name] = sys.modules[module_name]
        
        try:
            # Clear addon modules
            for module_name in addon_modules:
                if module_name in sys.modules:
                    del sys.modules[module_name]
            
            yield
            
        finally:
            # Restore addon modules
            for module_name, module in addon_modules.items():
                sys.modules[module_name] = module
    
    @contextmanager
    def isolated_database_test(self):
        """Context manager for isolated database testing"""
        # TODO: Implement database isolation
        # This would create a test database transaction and rollback after test
        try:
            yield
        finally:
            pass


class AddonTestIsolation:
    """Specialized isolation for addon tests"""
    
    def __init__(self, addon_name: str):
        self.addon_name = addon_name
        self.isolation = TestIsolation()
    
    def setup(self):
        """Setup addon test isolation"""
        self.isolation.setup_isolation()
        self._setup_addon_environment()
    
    def teardown(self):
        """Teardown addon test isolation"""
        self.isolation.teardown_isolation()
    
    def _setup_addon_environment(self):
        """Setup environment specific to addon testing"""
        # Ensure addon is available for import
        from erp.addons import ensure_addon_import
        
        addons_path = config.get('options', 'addons_path', fallback='addons')
        addon_path = os.path.join(addons_path, self.addon_name)
        
        if os.path.exists(addon_path):
            ensure_addon_import(self.addon_name, addon_path)


# Global isolation instance
_test_isolation = TestIsolation()


@contextmanager
def test_isolation():
    """Context manager for test isolation"""
    _test_isolation.setup_isolation()
    try:
        yield _test_isolation
    finally:
        _test_isolation.teardown_isolation()


@contextmanager
def addon_test_isolation(addon_name: str):
    """Context manager for addon test isolation"""
    isolation = AddonTestIsolation(addon_name)
    isolation.setup()
    try:
        yield isolation
    finally:
        isolation.teardown()
