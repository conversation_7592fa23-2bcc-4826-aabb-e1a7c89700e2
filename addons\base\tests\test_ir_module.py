"""
Tests for ir.module.module model in base addon
Demonstrates proper ERP test structure
"""
import sys
import os

# Add ERP core to path for testing
erp_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

from erp.tests.common import TransactionCase, SingleTransactionCase
from erp.tests.tags import tag, Tags
from erp.addons import get_addon_module


@tag(Tags.UNIT, Tags.BASE)
class TestIrModuleModule(TransactionCase):
    """Test cases for ir.module.module model"""
    
    def setUp(self):
        """Set up test case"""
        super().setUp()
        
        # Ensure base addon is loaded
        base_addon = get_addon_module('base')
        self.assertIsNotNone(base_addon, "Base addon should be available")
        
        # Get the model class
        self.IrModuleModule = self.env['ir.module.module']
    
    def test_create_module_record(self):
        """Test creating a module record"""
        module_data = {
            'name': 'test_module',
            'display_name': 'Test Module',
            'summary': 'A test module',
            'description': 'This is a test module for unit testing',
            'author': 'Test Author',
            'version': '1.0.0',
            'category': 'Test',
            'state': 'uninstalled'
        }
        
        module = self.IrModuleModule.create(module_data)
        
        self.assertEqual(module.name, 'test_module')
        self.assertEqual(module.display_name, 'Test Module')
        self.assertEqual(module.summary, 'A test module')
        self.assertEqual(module.author, 'Test Author')
        self.assertEqual(module.version, '1.0.0')
        self.assertEqual(module.category, 'Test')
        self.assertEqual(module.state, 'uninstalled')
    
    def test_module_name_required(self):
        """Test that module name is required"""
        with self.assertRaises(Exception):
            self.IrModuleModule.create({
                'display_name': 'Test Module',
                'state': 'uninstalled'
            })
    
    def test_module_display_name_required(self):
        """Test that module display name is required"""
        with self.assertRaises(Exception):
            self.IrModuleModule.create({
                'name': 'test_module',
                'state': 'uninstalled'
            })
    
    def test_module_state_values(self):
        """Test valid module state values"""
        valid_states = ['uninstalled', 'installed', 'to_install', 'to_upgrade', 'to_remove']
        
        for state in valid_states:
            module = self.IrModuleModule.create({
                'name': f'test_module_{state}',
                'display_name': f'Test Module {state}',
                'state': state
            })
            self.assertEqual(module.state, state)
    
    @tag(Tags.SLOW)
    def test_module_search_by_category(self):
        """Test searching modules by category"""
        # Create test modules in different categories
        categories = ['Test', 'Demo', 'Custom']
        modules = []
        
        for i, category in enumerate(categories):
            module = self.IrModuleModule.create({
                'name': f'test_module_{i}',
                'display_name': f'Test Module {i}',
                'category': category,
                'state': 'uninstalled'
            })
            modules.append(module)
        
        # Search for modules in 'Test' category
        test_modules = self.IrModuleModule.search([('category', '=', 'Test')])
        self.assertGreaterEqual(len(test_modules), 1)
        
        # Verify all found modules are in Test category
        for module in test_modules:
            self.assertEqual(module.category, 'Test')


@tag(Tags.INTEGRATION, Tags.BASE)
class TestIrModuleModuleIntegration(SingleTransactionCase):
    """Integration tests for ir.module.module model"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class"""
        super().setUpClass()
        
        # Ensure base addon is loaded
        base_addon = get_addon_module('base')
        cls.assertIsNotNone(cls, base_addon, "Base addon should be available")
        
        # Get the model class
        cls.IrModuleModule = cls.env['ir.module.module']
    
    def test_module_dependency_chain(self):
        """Test module dependency relationships"""
        # Create a chain of dependent modules
        base_module = self.IrModuleModule.create({
            'name': 'base_test',
            'display_name': 'Base Test Module',
            'state': 'installed'
        })
        
        dependent_module = self.IrModuleModule.create({
            'name': 'dependent_test',
            'display_name': 'Dependent Test Module',
            'depends': 'base_test',
            'state': 'uninstalled'
        })
        
        self.assertEqual(base_module.state, 'installed')
        self.assertEqual(dependent_module.state, 'uninstalled')
        self.assertEqual(dependent_module.depends, 'base_test')
    
    def test_module_lifecycle_states(self):
        """Test module lifecycle state transitions"""
        module = self.IrModuleModule.create({
            'name': 'lifecycle_test',
            'display_name': 'Lifecycle Test Module',
            'state': 'uninstalled'
        })
        
        # Test state transitions
        self.assertEqual(module.state, 'uninstalled')
        
        # Mark for installation
        module.state = 'to_install'
        self.assertEqual(module.state, 'to_install')
        
        # Install
        module.state = 'installed'
        self.assertEqual(module.state, 'installed')
        
        # Mark for upgrade
        module.state = 'to_upgrade'
        self.assertEqual(module.state, 'to_upgrade')
        
        # Back to installed
        module.state = 'installed'
        self.assertEqual(module.state, 'installed')
        
        # Mark for removal
        module.state = 'to_remove'
        self.assertEqual(module.state, 'to_remove')
        
        # Uninstall
        module.state = 'uninstalled'
        self.assertEqual(module.state, 'uninstalled')


@tag(Tags.UNIT, Tags.BASE, 'model_validation')
class TestIrModuleModuleValidation(TransactionCase):
    """Test validation logic for ir.module.module model"""
    
    def setUp(self):
        """Set up test case"""
        super().setUp()
        self.IrModuleModule = self.env['ir.module.module']
    
    def test_module_name_format(self):
        """Test module name format validation"""
        # Valid names
        valid_names = ['test_module', 'my_addon', 'sale_management', 'hr_payroll']
        
        for name in valid_names:
            module = self.IrModuleModule.create({
                'name': name,
                'display_name': f'Test {name}',
                'state': 'uninstalled'
            })
            self.assertEqual(module.name, name)
    
    def test_module_version_format(self):
        """Test module version format"""
        valid_versions = ['1.0.0', '2.1.3', '10.0.1.2.3', '1.0']
        
        for version in valid_versions:
            module = self.IrModuleModule.create({
                'name': f'test_module_{version.replace(".", "_")}',
                'display_name': f'Test Module {version}',
                'version': version,
                'state': 'uninstalled'
            })
            self.assertEqual(module.version, version)
    
    def test_module_unique_name(self):
        """Test that module names must be unique"""
        # Create first module
        self.IrModuleModule.create({
            'name': 'unique_test',
            'display_name': 'Unique Test Module',
            'state': 'uninstalled'
        })
        
        # Try to create another module with same name
        with self.assertRaises(Exception):
            self.IrModuleModule.create({
                'name': 'unique_test',
                'display_name': 'Another Unique Test Module',
                'state': 'uninstalled'
            })
